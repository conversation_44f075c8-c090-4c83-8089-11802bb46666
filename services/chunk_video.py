import os
from moviepy import VideoFileClip
from pydub import AudioSegment
import math

def chunk_video_audio(
    video_path,
    output_dir,
    chunk_length_min=3, # Target chunk length in minutes
    overlap_sec=30,     # Overlap in seconds
    output_format="wav" # Format for output chunks (wav, mp3, etc.)
):
    """
    Extracts audio from a video, chunks it into smaller segments with overlap,
    and saves the chunks to a specified directory.

    Args:
        video_path (str): Path to the input video file.
        output_dir (str): Directory where audio chunks will be saved.
        chunk_length_min (int): Desired length of each chunk in minutes.
        overlap_sec (int): Desired overlap between chunks in seconds.
        output_format (str): Audio format for the output chunks (e.g., 'wav', 'mp3').
    """
    print(f"Processing video: {video_path}")

    # --- 1. Create Output Directory ---
    os.makedirs(output_dir, exist_ok=True)
    print(f"Output directory: {output_dir}")

    # --- 2. Extract Audio using MoviePy ---
    try:
        video = VideoFileClip(video_path)
        audio = video.audio
        # Define a temporary path for the full audio
        base_filename = os.path.splitext(os.path.basename(video_path))[0]
        temp_audio_path = os.path.join(output_dir, f"{base_filename}_full_audio.wav") # Use WAV for intermediate lossless
        print("Extracting full audio track...")
        audio.write_audiofile(temp_audio_path, codec='pcm_s16le') # pcm_s16le is standard for WAV
        audio.close() # Close moviepy audio object
        video.close() # Close moviepy video object
        print(f"Full audio saved temporarily to: {temp_audio_path}")

    except Exception as e:
        print(f"Error extracting audio from video: {e}")
        # Clean up potential partial file if extraction failed mid-way
        if os.path.exists(temp_audio_path):
             try:
                 os.remove(temp_audio_path)
             except OSError as oe:
                 print(f"Warning: Could not remove temporary file {temp_audio_path}: {oe}")
        return

    # --- 3. Load Audio with Pydub ---
    try:
        print("Loading audio with pydub...")
        full_audio = AudioSegment.from_wav(temp_audio_path) # More specific if we know it's WAV
        print("Audio loaded successfully.")
    except Exception as e:
        print(f"Error loading audio file with pydub: {e}")
        # Clean up temporary file
        if os.path.exists(temp_audio_path):
             try:
                 os.remove(temp_audio_path)
             except OSError as oe:
                 print(f"Warning: Could not remove temporary file {temp_audio_path}: {oe}")
        return


    # --- 4. Chunk the Audio ---
    # Convert times to milliseconds (pydub works in ms)
    chunk_length_ms = chunk_length_min * 60 * 1000
    overlap_ms = overlap_sec * 1000
    total_duration_ms = len(full_audio)

    print(f"Total audio duration: {total_duration_ms / 1000:.2f} seconds")
    print(f"Chunk length: {chunk_length_ms / 1000:.2f} seconds")
    print(f"Overlap: {overlap_ms / 1000:.2f} seconds")

    start_time_ms = 0
    chunk_number = 0

    while start_time_ms < total_duration_ms:
        # Calculate end time for the chunk
        end_time_ms = start_time_ms + chunk_length_ms

        # Ensure the chunk doesn't go past the end of the audio
        # The slice end index is exclusive in pydub, so this is correct
        actual_end_time_ms = min(end_time_ms, total_duration_ms)

        # Extract the chunk
        print(f"Extracting chunk {chunk_number}: {start_time_ms / 1000:.2f}s - {actual_end_time_ms / 1000:.2f}s")
        audio_chunk = full_audio[start_time_ms:actual_end_time_ms]

        # Define output path for the chunk
        chunk_filename = f"chunk_{chunk_number:04d}.{output_format}"
        chunk_output_path = os.path.join(output_dir, chunk_filename)

        # Export the chunk
        try:
            print(f"Saving chunk {chunk_number} to {chunk_output_path}")
            audio_chunk.export(chunk_output_path, format=output_format)
        except Exception as e:
             print(f"Error exporting chunk {chunk_number}: {e}")
             # Decide if you want to stop or continue if one chunk fails
             # continue

        # Update start time for the next chunk
        # Move forward by chunk length MINUS overlap
        next_start_time_ms = start_time_ms + chunk_length_ms - overlap_ms

        # --- Crucial Check: Prevent infinite loops if overlap >= chunk_length ---
        if next_start_time_ms <= start_time_ms:
             if chunk_length_ms > 0: # Only advance if chunk length is positive
                print(f"Warning: Overlap ({overlap_ms}ms) is >= chunk length ({chunk_length_ms}ms) or next start is not progressing. Advancing by 1ms to prevent infinite loop.")
                next_start_time_ms = start_time_ms + 1 # Force minimal progress
             else:
                 print(f"Error: Chunk length ({chunk_length_ms}ms) is zero or negative. Aborting.")
                 break # Avoid infinite loop if chunk length is invalid


        # --- Check if the next chunk start is already past the end ---
        # This handles the case where the last segment is shorter than the overlap
        if next_start_time_ms >= total_duration_ms:
            print("Reached end of audio for starting next chunk.")
            break # Exit loop, we've processed the last relevant part

        start_time_ms = next_start_time_ms
        chunk_number += 1

        # --- Safety Break (optional, in case of unexpected loops) ---
        # if chunk_number > 10000: # Arbitrarily large number
        #     print("Error: Exceeded maximum chunk limit. Possible infinite loop?")
        #     break

    print(f"\nFinished chunking. {chunk_number + 1} chunks created.") # +1 because chunk_number is 0-indexed

    # --- 5. Clean up temporary full audio file ---
    print(f"Cleaning up temporary file: {temp_audio_path}")
    try:
        os.remove(temp_audio_path)
        print("Temporary file removed.")
    except OSError as e:
        print(f"Warning: Could not remove temporary file {temp_audio_path}: {e}")

# --- Example Usage ---
if __name__ == "__main__":
    input_video = "your_long_video.mp4"  # <--- CHANGE THIS to your video file path
    output_directory = "audio_chunks"    # <--- CHANGE THIS to your desired output folder

    # --- Check if input file exists ---
    if not os.path.exists(input_video):
        print(f"Error: Input video file not found at '{input_video}'")
        print("Please update the 'input_video' variable in the script.")
    else:
        chunk_video_audio(
            video_path=input_video,
            output_dir=output_directory,
            chunk_length_min=10,  # e.g., 10-minute chunks
            overlap_sec=30,       # e.g., 30-second overlap
            output_format="wav"   # Use WAV for best quality for ASR, or mp3 for smaller files
        )

        print(f"\nAudio chunks saved in: {os.path.abspath(output_directory)}")
        print("You can now process these chunks individually with your LLM/ASR model.")
        print("Remember to handle the overlap when stitching the transcriptions back together!")