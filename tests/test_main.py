"""
Tests for the main FastAPI application.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
import json

# Mock the settings and clients before importing main
with patch('main.create_clients') as mock_create_clients:
    mock_redis = Mock()
    mock_gemini = Mock()
    mock_openai = Mock()
    mock_elevenlabs = Mock()
    
    mock_create_clients.return_value = (mock_gemini, mock_openai, mock_elevenlabs, mock_redis)
    
    from main import app

client = TestClient(app)


class TestHealthEndpoint:
    """Test health check endpoint."""
    
    def test_health_check_success(self):
        """Test successful health check."""
        with patch('main.redis_client') as mock_redis:
            mock_redis.ping.return_value = True
            
            response = client.get("/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert "version" in data
            assert "timestamp" in data
    
    def test_health_check_redis_failure(self):
        """Test health check when Red<PERSON> is down."""
        with patch('main.redis_client') as mock_redis:
            mock_redis.ping.side_effect = Exception("Redis connection failed")
            
            response = client.get("/health")
            
            assert response.status_code == 503
            assert "Service unavailable" in response.json()["detail"]


class TestSessionEndpoints:
    """Test session management endpoints."""
    
    def test_start_session_success(self):
        """Test successful session creation."""
        with patch('main.redis_client') as mock_redis:
            mock_redis.sadd.return_value = 1
            mock_redis.set.return_value = True
            mock_redis.expire.return_value = True
            
            response = client.post("/start-session")
            
            assert response.status_code == 200
            data = response.json()
            assert "session_id" in data
            assert len(data["session_id"]) > 0
    
    def test_start_session_redis_failure(self):
        """Test session creation when Redis fails."""
        with patch('main.redis_client') as mock_redis:
            mock_redis.sadd.side_effect = Exception("Redis error")
            
            response = client.post("/start-session")
            
            assert response.status_code == 500
            assert "Failed to create session" in response.json()["detail"]


class TestStreamEndpoint:
    """Test audio streaming endpoint."""
    
    def test_stream_chunk_processing(self):
        """Test audio chunk processing."""
        payload = {
            "session_id": "test-session-123",
            "audio_data": "dGVzdCBhdWRpbyBkYXRh",  # base64 encoded "test audio data"
            "chunk_number": 1,
            "stream_type": "chunk"
        }
        
        with patch('main.get_transcribe_service') as mock_service:
            mock_transcribe = Mock()
            mock_transcribe.process_chunk.return_value = None
            mock_service.return_value = mock_transcribe
            
            response = client.post("/stream", json=payload)
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "chunk_processed"
            assert data["chunk_number"] == 1
    
    def test_stream_invalid_payload(self):
        """Test stream endpoint with invalid payload."""
        payload = {
            "session_id": "",  # Invalid empty session ID
            "audio_data": "test",
            "chunk_number": -1,  # Invalid negative chunk number
            "stream_type": "invalid"  # Invalid stream type
        }
        
        response = client.post("/stream", json=payload)
        
        assert response.status_code == 422  # Validation error


class TestAnalyzeEndpoint:
    """Test video analysis endpoint."""
    
    def test_analyze_video_success(self):
        """Test successful video analysis."""
        payload = {
            "uri": "https://example.com/test-video.mp4"
        }
        
        mock_result = {
            "video_uri": payload["uri"],
            "analysis": "Test analysis result",
            "status": "completed"
        }
        
        with patch('main.get_video_analysis_service') as mock_service:
            mock_video_service = Mock()
            mock_video_service.analyze.return_value = mock_result
            mock_service.return_value = mock_video_service
            
            response = client.post("/analyze", json=payload)
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "completed"
            assert data["video_uri"] == payload["uri"]
    
    def test_analyze_invalid_uri(self):
        """Test video analysis with invalid URI."""
        payload = {
            "uri": ""  # Invalid empty URI
        }
        
        response = client.post("/analyze", json=payload)
        
        assert response.status_code == 422  # Validation error


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    with patch('main.redis_client') as mock:
        yield mock


@pytest.fixture
def mock_ai_clients():
    """Mock AI service clients for testing."""
    with patch('main.gemini_client') as mock_gemini, \
         patch('main.openai_client') as mock_openai, \
         patch('main.elevenlabs_client') as mock_elevenlabs:
        yield {
            'gemini': mock_gemini,
            'openai': mock_openai,
            'elevenlabs': mock_elevenlabs
        }
