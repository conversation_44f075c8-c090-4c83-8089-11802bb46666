[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "python-chat-api"
version = "1.0.0"
description = "AI-powered interview system with audio transcription and question generation"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    "fastapi>=0.115.11",
    "uvicorn[standard]>=0.34.0",
    "pydantic>=2.10.6",
    "pydantic-settings>=2.7.0",
    "python-dotenv>=1.0.1",
    "redis>=5.2.1",
    "openai>=1.68.0",
    "google-genai>=1.7.0",
    "elevenlabs>=1.54.0",
    "pydub>=0.25.1",
    "httpx>=0.28.1",
    "requests>=2.32.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.3.4",
    "pytest-asyncio>=0.25.0",
    "httpx>=0.28.1",
    "black>=24.0.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--asyncio-mode=auto",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["config", "services"]

[tool.coverage.run]
source = ["."]
omit = [
    "tests/*",
    ".venv/*",
    "scripts/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
]
