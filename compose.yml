# Comments are provided throughout this file to help you get started.
# If you need more help, visit the Docker Compose reference guide at
# https://docs.docker.com/go/compose-spec-reference/

# Here the instructions define your application as a service called "server".
# This service is built from the Dockerfile in the current directory.
# You can add other services your application may depend on here, such as a
# database or a cache. For examples, see the Awesome Compose repository:
# https://github.com/docker/awesome-compose
services:
  server:
    build:
      context: .
    ports:
      - 8000:8000
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=/run/secrets/postgres_password
      - POSTGRES_DB=rakamin_development
      - GEMINI_API_KEY=/run/secrets/gemini_api_key
      - OPENAI_API_KEY=/run/secrets/openai_api_key
      - OPEN_ROUTER_API_KEY=/run/secrets/open_router_api_key
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    secrets:
      - postgres_password
      - gemini_api_key
      - openai_api_key
      - open_router_api_key
    develop:
      watch:
        - action: rebuild
          path: .
  
  db:
    image: pgvector/pgvector:0.8.0-pg17
    restart: always
    user: postgres
    secrets:
      - postgres_password
    environment:
      - POSTGRES_PASSWORD=/run/secrets/postgres_password
      - POSTGRES_DB=rakamin_development
    expose:
      - 5432
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres", "-d", "rakamin_development"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:latest
    restart: always
    expose:
      - 6379
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  redis_data:
secrets:
  postgres_password:
    file: ./secrets/postgress_password.txt
  gemini_api_key:
    file: ./secrets/gemini_api_key.txt
  openai_api_key:
    file: ./secrets/openai_api_key.txt
  open_router_api_key:
    file: ./secrets/open_router_api_key.txt