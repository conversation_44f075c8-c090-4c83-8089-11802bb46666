# Application Configuration
APP_NAME=Python Chat API
APP_VERSION=1.0.0
DEBUG=false
HOST=0.0.0.0
PORT=8000

# Security
SECRET_KEY=your-secret-key-change-in-production
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# PostgreSQL Configuration (optional)
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_DB=rakamin_development

# AI Service API Keys (REQUIRED)
GEMINI_API_KEY=
OPENAI_API_KEY=your-openai-api-key-here
ELEVEN_LABS_API_KEY=your-elevenlabs-api-key-here
OPEN_ROUTER_API_KEY=your-open-router-api-key-here

# Audio Processing
MAX_AUDIO_CHUNK_SIZE=26214400  # 25MB
TEMP_DIR=./tmp

# Session Configuration
SESSION_TIMEOUT=3600           # 1 hour
MAX_SESSIONS_PER_USER=5

# Logging
LOG_LEVEL=INFO
LOG_FILE=
